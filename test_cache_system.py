#!/usr/bin/env python3
"""
测试IP地址缓存系统的增量保存功能
验证缓存是否能在处理中断时正确保存数据
"""

import os
import json
import tempfile
import time
from merge_dbf import IPLocationQuerier

def test_incremental_caching():
    """测试增量缓存功能"""
    print("🧪 开始测试IP地址增量缓存系统...")
    
    # 创建临时测试文件
    test_data = """客户编号,IIP重复值,其他信息
001,***********,测试数据1
002,********,测试数据2
003,**********,测试数据3
004,*******,测试数据4
005,*******,测试数据5"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
        f.write(test_data)
        test_file = f.name
    
    try:
        # 测试缓存写入始终启用
        print("\n📋 测试1: 验证缓存写入始终启用...")
        
        # 创建查询器实例（禁用缓存读取）
        querier = IPLocationQuerier(test_file, use_cache=False)
        
        # 检查缓存文件路径
        cache_file = querier.cache_file_path
        print(f"📁 缓存文件路径: {cache_file}")
        
        # 清理可能存在的旧缓存文件
        if os.path.exists(cache_file):
            os.remove(cache_file)
            print("🗑️ 清理旧缓存文件")
        
        # 模拟单个IP查询并立即保存到缓存
        test_ip = "***********"
        test_location = "测试地址-北京"
        
        print(f"\n🔍 模拟查询IP: {test_ip}")
        querier._save_single_ip_to_cache(test_ip, test_location)
        
        # 检查缓存文件是否立即创建
        if os.path.exists(cache_file):
            print("✅ 缓存文件已立即创建")
            
            # 读取缓存内容
            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            print(f"📊 缓存内容: {cache_data}")
            
            if test_ip in cache_data and cache_data[test_ip] == test_location:
                print("✅ 缓存数据正确保存")
            else:
                print("❌ 缓存数据保存错误")
        else:
            print("❌ 缓存文件未创建")
        
        # 测试批量保存
        print(f"\n📋 测试2: 验证批量保存机制...")
        
        test_ips = [
            ("********", "测试地址-上海"),
            ("**********", "测试地址-广州"),
            ("*******", "测试地址-美国"),
            ("*******", "测试地址-美国")
        ]
        
        for ip, location in test_ips:
            print(f"🔍 保存IP: {ip} -> {location}")
            querier._save_single_ip_to_cache(ip, location)
            time.sleep(0.1)  # 短暂延迟模拟真实查询
        
        # 检查最终缓存状态
        if os.path.exists(cache_file):
            with open(cache_file, 'r', encoding='utf-8') as f:
                final_cache = json.load(f)
            
            print(f"📊 最终缓存包含 {len(final_cache)} 条记录:")
            for ip, location in final_cache.items():
                print(f"   {ip} -> {location}")
            
            # 验证所有IP都已保存
            expected_count = len(test_ips) + 1  # 包括第一个测试IP
            if len(final_cache) >= expected_count:
                print("✅ 所有IP地址都已正确保存到缓存")
            else:
                print(f"⚠️ 缓存记录数量不足，期望至少 {expected_count} 条，实际 {len(final_cache)} 条")
        
        # 测试缓存读取功能
        print(f"\n📋 测试3: 验证缓存读取功能...")
        
        # 创建新的查询器实例（启用缓存读取）
        querier2 = IPLocationQuerier(test_file, use_cache=True)
        
        # 检查是否正确加载了缓存
        loaded_cache_size = len(querier2.persistent_cache)
        print(f"📊 新实例加载缓存: {loaded_cache_size} 条记录")
        
        if loaded_cache_size > 0:
            print("✅ 缓存读取功能正常")
            
            # 测试缓存命中
            for ip in ["***********", "********"]:
                cached_location = querier2._get_cached_location(ip)
                if cached_location:
                    print(f"✅ 缓存命中: {ip} -> {cached_location}")
                else:
                    print(f"❌ 缓存未命中: {ip}")
        else:
            print("❌ 缓存读取失败")
        
        print("\n🎉 缓存系统测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        try:
            if os.path.exists(test_file):
                os.remove(test_file)
            
            # 可选：清理测试缓存文件
            # if os.path.exists(cache_file):
            #     os.remove(cache_file)
            #     print("🗑️ 清理测试缓存文件")
        except:
            pass

if __name__ == "__main__":
    test_incremental_caching()
