#!/usr/bin/env python3
"""
简单的缓存系统测试
"""

import os
import json
import tempfile

def test_basic_cache():
    """测试基本缓存功能"""
    print("🧪 开始基本缓存测试...")
    
    # 创建临时测试文件
    test_data = "客户编号,IIP重复值\n001,***********\n002,********"
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
        f.write(test_data)
        test_file = f.name
    
    try:
        # 导入模块
        from merge_dbf import IPLocationQuerier
        print("✅ 模块导入成功")
        
        # 创建查询器实例
        querier = IPLocationQuerier(test_file, use_cache=False)
        print("✅ 查询器实例创建成功")
        
        # 检查缓存文件路径
        cache_file = querier.cache_file_path
        print(f"📁 缓存文件路径: {cache_file}")
        
        # 清理可能存在的旧缓存文件
        if os.path.exists(cache_file):
            os.remove(cache_file)
            print("🗑️ 清理旧缓存文件")
        
        # 测试单个IP保存
        test_ip = "***********"
        test_location = "测试地址-北京"
        
        print(f"🔍 测试保存IP: {test_ip} -> {test_location}")
        querier._save_single_ip_to_cache(test_ip, test_location)
        
        # 检查缓存文件是否创建
        if os.path.exists(cache_file):
            print("✅ 缓存文件已创建")
            
            # 读取缓存内容
            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            print(f"📊 缓存内容: {cache_data}")
            
            if test_ip in cache_data and cache_data[test_ip] == test_location:
                print("✅ 缓存数据正确")
            else:
                print("❌ 缓存数据错误")
        else:
            print("❌ 缓存文件未创建")
        
        print("🎉 基本缓存测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        try:
            if os.path.exists(test_file):
                os.remove(test_file)
        except:
            pass

if __name__ == "__main__":
    test_basic_cache()
